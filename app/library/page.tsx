'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'

interface LibraryBook {
  id: string
  title: string
  cover_image_url: string | null
  author_name: string
  access_type: 'purchased' | 'free' | 'preview'
  added_at: string
  last_accessed_at: string
  total_chapters: number
  total_words: number
  reading_time_minutes: number
  users: {
    name: string
    avatar: string | null
  }
}

export default function LibraryPage() {
  const [books, setBooks] = useState<LibraryBook[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/auth/login')
      return
    }
    setUser(user)
    fetchLibraryBooks(user.id)
  }

  const fetchLibraryBooks = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_library')
        .select(`
          access_type,
          added_at,
          last_accessed_at,
          projects!inner(
            id,
            title,
            cover_image_url,
            author_name,
            total_chapters,
            total_words,
            reading_time_minutes,
            users!inner(name, avatar)
          )
        `)
        .eq('user_id', userId)
        .order('added_at', { ascending: false })

      if (error) {
        console.error('Error fetching library:', error)
        return
      }

      const libraryBooks = data?.map((item: any) => ({
        id: item.projects.id,
        title: item.projects.title,
        cover_image_url: item.projects.cover_image_url,
        author_name: item.projects.author_name,
        access_type: item.access_type,
        added_at: item.added_at,
        last_accessed_at: item.last_accessed_at,
        total_chapters: item.projects.total_chapters,
        total_words: item.projects.total_words,
        reading_time_minutes: item.projects.reading_time_minutes,
        users: item.projects.users
      })) || []

      setBooks(libraryBooks)
    } catch (error) {
      console.error('Error fetching library books:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatReadingTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const formatPages = (words: number) => {
    return Math.ceil((words / 1000) * 4)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your library...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-serif text-gray-900 mb-2">My Library</h1>
          <p className="text-gray-600">Your collection of purchased and free books</p>
        </div>

        {books.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📚</div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Your library is empty</h2>
            <p className="text-gray-600 mb-6">Start building your collection by purchasing or downloading free books</p>
            <Link href="/books">
              <Button size="lg">Browse Books</Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {books.map((book) => (
              <div key={book.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                {/* Book Cover */}
                <div className="aspect-[3/4] relative bg-gray-100">
                  {book.cover_image_url ? (
                    <Image
                      src={book.cover_image_url}
                      alt={book.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <div className="text-4xl">📖</div>
                    </div>
                  )}
                  
                  {/* Access Type Badge */}
                  <div className="absolute top-2 right-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      book.access_type === 'purchased' 
                        ? 'bg-green-100 text-green-800'
                        : book.access_type === 'free'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {book.access_type === 'purchased' ? 'Owned' : 
                       book.access_type === 'free' ? 'Free' : 'Preview'}
                    </span>
                  </div>
                </div>

                {/* Book Info */}
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">{book.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">by {book.users.name}</p>
                  
                  {/* Book Stats */}
                  <div className="text-xs text-gray-500 mb-3 space-y-1">
                    <div className="flex justify-between">
                      <span>{book.total_chapters} chapters</span>
                      <span>~{formatPages(book.total_words)} pages</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{formatReadingTime(book.reading_time_minutes)} read</span>
                      <span>Added {new Date(book.added_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    <Link href={`/books/${book.id}/read`} className="block">
                      <Button className="w-full" size="sm">
                        📖 {book.access_type === 'preview' ? 'Preview' : 'Read'}
                      </Button>
                    </Link>
                    <Link href={`/books/${book.id}`} className="block">
                      <Button variant="outline" className="w-full" size="sm">
                        📋 Details
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
